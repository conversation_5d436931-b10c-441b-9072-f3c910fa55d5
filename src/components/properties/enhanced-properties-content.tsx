"use client";

import { useQuery } from "@tanstack/react-query";
import { MapPin, Plus, Search, SlidersHorizontal } from "lucide-react";
import { useMemo, useState } from "react";
import { EnhancedPropertyCard } from "@/components/properties/enhanced-property-card";
import { NewProperty } from "@/components/properties/new-property";
import { useTRPC } from "@/components/trpc/client";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { CardGrid } from "@/components/ui/responsive-grid";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";

type SortOption = "name" | "created" | "address" | "recent-activity";
type ViewMode = "grid" | "list";

export function EnhancedPropertiesContent() {
  const trpc = useTRPC();
  const [searchQuery, setSearchQuery] = useState("");
  const [sortBy, setSortBy] = useState<SortOption>("name");
  const [viewMode, setViewMode] = useState<ViewMode>("grid");
  const [showFilters, setShowFilters] = useState(false);

  const { data: properties, isLoading } = useQuery(
    trpc.properties.list.queryOptions(),
  );

  // Enhanced filtering and sorting
  const filteredAndSortedProperties = useMemo(() => {
    if (!properties) return [];

    const filtered = properties.filter((property) => {
      const searchLower = searchQuery.toLowerCase();
      const nameMatch = property.name.toLowerCase().includes(searchLower);
      const addressMatch = property.address
        ? `${property.address.street} ${property.address.city} ${property.address.state}`
            .toLowerCase()
            .includes(searchLower)
        : false;

      return nameMatch || addressMatch;
    });

    // Sort properties
    filtered.sort((a, b) => {
      switch (sortBy) {
        case "name":
          return a.name.localeCompare(b.name);
        case "created":
          return (
            new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime()
          );
        case "address": {
          const aAddress = a.address
            ? `${a.address.city}, ${a.address.state}`
            : "";
          const bAddress = b.address
            ? `${b.address.city}, ${b.address.state}`
            : "";
          return aAddress.localeCompare(bAddress);
        }
        case "recent-activity":
          // This would require additional data about recent jobs/activity
          return (
            new Date(b.updatedAt || b.createdAt).getTime() -
            new Date(a.updatedAt || a.createdAt).getTime()
          );
        default:
          return 0;
      }
    });

    return filtered;
  }, [properties, searchQuery, sortBy]);

  // Get statistics
  const stats = useMemo(() => {
    if (!properties) return { total: 0, cities: 0, states: 0 };

    const cities = new Set(
      properties.map((p) => p.address?.city).filter(Boolean),
    );
    const states = new Set(
      properties.map((p) => p.address?.state).filter(Boolean),
    );

    return {
      total: properties.length,
      cities: cities.size,
      states: states.size,
    };
  }, [properties]);

  if (isLoading) {
    return (
      <div className="space-y-6">
        {/* Header Skeleton */}
        <div className="flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between">
          <div className="space-y-2">
            <div className="h-8 w-48 animate-pulse rounded bg-muted" />
            <div className="h-4 w-64 animate-pulse rounded bg-muted" />
          </div>
          <div className="h-10 w-32 animate-pulse rounded bg-muted" />
        </div>

        {/* Stats Skeleton */}
        <div className="grid gap-4 sm:grid-cols-3">
          {Array.from({ length: 3 }).map((_, i) => (
            <div key={i} className="h-20 animate-pulse rounded-lg bg-muted" />
          ))}
        </div>

        {/* Search/Filter Skeleton */}
        <div className="flex flex-col gap-4 sm:flex-row">
          <div className="h-10 flex-1 animate-pulse rounded bg-muted" />
          <div className="h-10 w-32 animate-pulse rounded bg-muted" />
        </div>

        {/* Grid Skeleton */}
        <CardGrid>
          {Array.from({ length: 6 }).map((_, i) => (
            <div key={i} className="h-80 animate-pulse rounded-lg bg-muted" />
          ))}
        </CardGrid>
      </div>
    );
  }

  if (!properties || properties.length === 0) {
    return (
      <div className="space-y-6">
        {/* Empty State Header */}
        <div className="py-12 text-center">
          <div className="mx-auto mb-4 flex h-24 w-24 items-center justify-center rounded-full bg-muted">
            <MapPin className="h-12 w-12 text-muted-foreground" />
          </div>
          <h3 className="mb-2 font-semibold text-lg">No Properties Yet</h3>
          <p className="mx-auto mb-6 max-w-md text-muted-foreground">
            Get started by adding your first property. You can then create
            projects and manage work for each location.
          </p>
        </div>

        <CardGrid>
          <NewProperty />
        </CardGrid>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between">
        <div>
          <h1 className="font-bold text-2xl tracking-tight">Properties</h1>
          <p className="text-muted-foreground">
            Manage your properties and track project history
          </p>
        </div>
        <Button
          onClick={() =>
            document.getElementById("new-property-trigger")?.click()
          }
        >
          <Plus className="mr-2 h-4 w-4" />
          Add Property
        </Button>
      </div>

      {/* Statistics Cards */}
      <div className="grid gap-4 sm:grid-cols-3">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-3">
              <div className="rounded-lg bg-tradecrews-orange/10 p-2">
                <MapPin className="h-5 w-5 text-tradecrews-orange-600" />
              </div>
              <div>
                <p className="font-bold text-2xl">{stats.total}</p>
                <p className="text-muted-foreground text-sm">
                  Total Properties
                </p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-3">
              <div className="rounded-lg bg-blue-100 p-2">
                <MapPin className="h-5 w-5 text-blue-600" />
              </div>
              <div>
                <p className="font-bold text-2xl">{stats.cities}</p>
                <p className="text-muted-foreground text-sm">Cities</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-3">
              <div className="rounded-lg bg-green-100 p-2">
                <MapPin className="h-5 w-5 text-green-600" />
              </div>
              <div>
                <p className="font-bold text-2xl">{stats.states}</p>
                <p className="text-muted-foreground text-sm">States</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Search and Filters */}
      <div className="flex flex-col gap-4 sm:flex-row sm:items-center">
        <div className="relative flex-1">
          <Search className="-translate-y-1/2 absolute top-1/2 left-3 h-4 w-4 text-muted-foreground" />
          <Input
            placeholder="Search properties by name or address..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="pl-9"
          />
        </div>

        <div className="flex items-center gap-2">
          <Select
            value={sortBy}
            onValueChange={(value: SortOption) => setSortBy(value)}
          >
            <SelectTrigger className="w-48">
              <SelectValue placeholder="Sort by..." />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="name">Name (A-Z)</SelectItem>
              <SelectItem value="created">Recently Added</SelectItem>
              <SelectItem value="address">Location</SelectItem>
              <SelectItem value="recent-activity">Recent Activity</SelectItem>
            </SelectContent>
          </Select>

          <Button
            variant="outline"
            size="icon"
            onClick={() => setShowFilters(!showFilters)}
          >
            <SlidersHorizontal className="h-4 w-4" />
          </Button>
        </div>
      </div>

      {/* Results Summary */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-2">
          <span className="text-muted-foreground text-sm">
            Showing {filteredAndSortedProperties.length} of {properties.length}{" "}
            properties
          </span>
          {searchQuery && (
            <Badge variant="secondary" className="text-xs">
              Search: "{searchQuery}"
            </Badge>
          )}
        </div>
      </div>

      {/* Properties Grid */}
      {filteredAndSortedProperties.length === 0 ? (
        <div className="py-12 text-center">
          <Search className="mx-auto mb-4 h-12 w-12 text-muted-foreground" />
          <h3 className="mb-2 font-semibold text-lg">No Properties Found</h3>
          <p className="mb-4 text-muted-foreground">
            Try adjusting your search terms or filters
          </p>
          <Button variant="outline" onClick={() => setSearchQuery("")}>
            Clear Search
          </Button>
        </div>
      ) : (
        <CardGrid>
          {filteredAndSortedProperties.map((property) => (
            <EnhancedPropertyCard
              key={property.id}
              property={property}
              showStats={true}
              compact={viewMode === "list"}
            />
          ))}
          <NewProperty />
        </CardGrid>
      )}
    </div>
  );
}
